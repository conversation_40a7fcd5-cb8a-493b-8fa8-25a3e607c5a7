import { STOCK_STATE } from '@/lib/const';
import { syncStock } from '@/mutations/item/inventory/stock/sync';
import { processAnalytics } from '@/queries/get';
import cors from '@fastify/cors';
import fastifyRedis from '@fastify/redis';
import dayjs from 'dayjs';
import fastify from 'fastify';
import fastifyCron from 'fastify-cron';
import { promises } from 'fs';
import { first, last, sample } from 'lodash';
import puppeteer from 'puppeteer';
import { getPrinters, isPrintComplete, print } from 'unix-print';
import { ExecResponse, Printer } from 'unix-print/build/types';
import fastifyRaven from "fastify-ravendb"

const server = fastify();
server.register(cors, { origin: '*' });
server.register(fastifyRedis, { url: process.env.REDIS_URL });
server.register(fastifyRaven, { url: process.env.RAVENDB_URL });
server.register(fastifyCron, {
    jobs: [
        {
            cronTime: '0 */2 * * *', // Every 2 hours
            start: true,
            onTick: () => {
                const dir = 'public/storage/document';
                console.info('Janitor running ...');

                // Handle async operations without blocking the cron job
                promises
                    .stat(dir)
                    .then((stat) => {
                        if (stat.isDirectory()) {
                            return promises.readdir(dir);
                        }

                        return [];
                    })
                    .then((junks) => {
                        for (const junk of junks) {
                            promises
                                .stat(`${dir}/${junk}`)
                                .then((trash) => {
                                    if (trash.isFile()) {
                                        const names = junk.split('-');

                                        if (['receipt', 'sales'].includes(first(names) ?? '') && last(names)?.toLowerCase()?.endsWith('.pdf')) {
                                            const timestamp = Number(last(names)?.replace('.pdf', ''));

                                            if (dayjs().diff(dayjs.unix(timestamp), 'minutes') >= 3) {
                                                promises.unlink(`${dir}/${junk}`).catch((error) => {
                                                    console.error('Error deleting file:', junk, error);
                                                });
                                            }
                                        }
                                    }
                                })
                                .catch((error) => {
                                    console.error('Error checking file:', junk, error);
                                });
                        }
                    })
                    .catch((error) => {
                        console.error('Error in cron job:', error);
                    });
            }
        },
        {
            cronTime: '*/3 * * * *', // Every 3 minutes
            start: true,
            onTick: () => {
                const { redis } = server;
                redis.get(STOCK_STATE.SYNC).then((state) => {
                    if (!state) {
                        console.info('Analytics running ...');
                        processAnalytics().catch((error) => console.error(error));
                    }
                });
            }
        }
    ]
});

const pickRandPrinter = async () => {
    let printer: Printer | undefined;

    try {
        const printers = await getPrinters();
        printer = sample(printers.filter(({ status }) => status === 'idle'));
    } catch (error) {
        console.error(error);
    }

    return printer ?? null;
};

const awaitPrinting = async (job: ExecResponse) => {
    while (!(await isPrintComplete(job))) {
        await new Promise((resolve) => setTimeout(resolve, 1500)); // Wait for 1.5 seconds
    }

    return true;
};

const stockTaking = async () => {
    console.log('Stock taking running ...');
};

server.get('/', (_, response) => response.send({ status: 'Running ...' }));

server.get('/stock', async (_, response) => {
    const { redis } = server;
    await redis.set(STOCK_STATE.SYNC, 'true');
    await syncStock();
    await redis.set(STOCK_STATE.STOP, 'true');
    await redis.del(STOCK_STATE.SYNC, STOCK_STATE.CACHED);
    response.send({ status: 'Synced ...' });
});

server.get('/print/:section/:id', async (request, response) => {
    const { section, id } = request.params as any;
    let printed = false;

    if (id && section) {
        const printPath = `public/storage/document/${section}-${id}-${dayjs().unix()}.pdf`;

        try {
            const browser = await puppeteer.launch({ headless: true, args: ['--disable-dev-shm-usage', '--no-sandbox', '--disable-setuid-sandbox'] });
            const page = await browser.newPage();
            await page.goto(`${process.env.NEXT_PUBLIC_BASE_URL ?? ''}/printout/${section}/${id}`, { waitUntil: 'networkidle0' });
            const generated = await page.pdf({ path: printPath, format: 'A4', landscape: true });
            await browser.close();

            if (generated) {
                const device = await pickRandPrinter();

                if (device) {
                    const job = await print(printPath, device.printer, ['-o landscape', '-o fit-to-page', '-o media=A4']);
                    printed = await awaitPrinting(job);
                }
            }
        } catch (error) {
            console.error(error);
        }
    }

    response.send({ printed });
});

server.get('/take/stock', async (_, response) => {
    const { redis } = server;
    await redis.set(STOCK_STATE.TAKE, 'true');
    await stockTaking();
    response.send({ status: 'Archived ...' });
});

const goRun = async () => {
    try {
        await server.listen({ port: 5000 });
        console.info('Fastify server is running ...');
    } catch (_) {
        console.error(_);
    }
};

goRun();
