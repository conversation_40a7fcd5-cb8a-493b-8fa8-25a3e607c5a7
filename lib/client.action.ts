'user client';

import { InfoDocument } from '@/models/info.schema';
import { ProductDocument } from '@/models/product.schema';
import { getDataNoParam } from '@/queries/get';
import { createHash } from 'crypto';
import { isEmpty, isNaN } from 'lodash';
import { Toast } from 'primereact/toast';
import { getSignedSession, isArchiving } from './server.action';

const getDefaultImage = (name: string) => `/storage/image/global/${name}.jpg`;
const parseUnit = (item: any) => ({ code: item?._id, name: item?.name });
const getUnitDetail = (item: any, unit: string) => (item?._id === unit ? item : null);

const doCancelAction = (path: string) => (window.location.href = `/${path}`);

const getDefaultLogo = () => getDefaultImage('logo');

const getDefaultPhoto = () => getDefaultImage('photo');

const getDefaultProduct = () => getDefaultImage('product');

const formatRp = (value = 0, discount = 0) => {
    let rpString = 'Rp 0';

    try {
        if (!isNaN(value)) {
            let final = value;

            if (discount > 0) {
                final = value - (discount / 100) * value;
            }

            rpString = new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(final).replace(',00', '').replace('Rp', 'Rp ');
        } else {
            rpString = 'Rp 0.00';
        }
    } catch (_) {
        console.error(_);
    }

    return rpString;
};

const formatNumber = (value = 0) => {
    let numberString = '0';

    try {
        if (!isNaN(value)) {
            numberString = new Intl.NumberFormat('id-ID', { style: 'decimal', maximumFractionDigits: 2 }).format(value);
        }
    } catch (_) {
        console.error(_);
    }

    return numberString;
};

const toaster = (toast: Toast | null, messages: { severity: 'success' | 'info' | 'warn' | 'error'; summary: string; detail: string }[], reload?: string) => {
    messages.forEach(({ severity, summary, detail }) => toast?.show({ life: 3000, severity, summary, detail }));

    if (reload) {
        setTimeout(() => doCancelAction(reload), 2500);
    }
};

const provideValidPassword = (password?: string): string => {
    let hashed: string | null = null;

    if (password) {
        // Regular expression to match a valid MD5 hash, hash password if not valid md5 string
        if (/^[a-f0-9]{32}$/.test(password)) {
            hashed = password;
        } else {
            hashed = createHash('md5').update(password).digest('hex');
        }
    }

    return hashed ?? '';
};

const getAppInfo = async (): Promise<InfoDocument | null> => (await getDataNoParam('info')) ?? null;

const pickUnitDetail = (item: any, unit: string) => {
    let unitDetail = getUnitDetail(item?.unit, unit);
    unitDetail ??= getUnitDetail(item?.bundle?.node?.unit, unit);

    return unitDetail;
};

const initUnits = (item: ProductDocument) => {
    const options = [parseUnit(item?.unit)];

    if (item?.bundle) {
        options.push(parseUnit(item.bundle.node?.unit));
    }

    return options;
};

const handleFailedSave = (toast: Toast | null, notices: string[]) => {
    if (notices.length > 0) {
        toaster(
            toast,
            notices.map((detail) => ({ severity: 'warn', summary: 'Validasi gagal!', detail }))
        );
    } else {
        toaster(toast, [{ severity: 'warn', summary: 'Gagal simpan!', detail: 'Data tidak dapat disimpan oleh Sistem' }]);
    }
};

const calculateSumCost = (products: any[]) => {
    let sumCost = 0;

    products.forEach((item) => {
        let amount = item?.cost ?? item?.price ?? 0;

        if (item?.discount > 0) {
            amount = amount - (item?.discount / 100) * amount;
        }

        sumCost += amount;
    });

    return sumCost;
};

const processRangePrice = (cost: number[]) => {
    let costString = '';

    if (!isEmpty(cost)) {
        costString = cost?.[1] ? `${formatRp(cost[0])} - ${formatRp(cost[1])}` : formatRp(cost?.[0] || 0);

        if (cost[0] === cost[1]) {
            costString = formatRp(cost[0]);
        }

        if (cost[0] === 0) {
            costString = formatRp(cost[1]);
        }
    }

    return costString;
};

const useSession = () => {
    const scanning = async () => {
        const signed = await getSignedSession();

        if (!signed?._id) {
            window.location.href = '/auth';
        } else if (await isArchiving()) {
            window.location.href = '/stock-taking';
        }
    };

    const archiving = async () => {
        const signed = await getSignedSession();

        if (!signed?._id) {
            window.location.href = '/auth';
        } else if (!(await isArchiving())) {
            window.location.href = '/';
        }
    };

    return { scanning, archiving };
};

export { calculateSumCost, doCancelAction, formatNumber, formatRp, getAppInfo, getDefaultLogo, getDefaultPhoto, getDefaultProduct, handleFailedSave, initUnits, pickUnitDetail, processRangePrice, provideValidPassword, toaster, useSession };
